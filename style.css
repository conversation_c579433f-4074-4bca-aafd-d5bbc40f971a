* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    margin: 0;
}

.container {
    width: 100%;
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    position: relative;
    box-sizing: border-box;
}

h1 {
    text-align: center;
    color: white;
    font-size: 2.5rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.main-grid {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 30px;
    flex: 1;
    min-height: 0;
    position: relative;
}

.main-grid.panel-hidden {
    grid-template-columns: 0 1fr;
    gap: 0;
}

.employee-section {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    width: 400px;
    transition: all 0.3s ease;
    position: relative;
}

.main-grid.panel-hidden .employee-section {
    width: 0;
    padding: 0;
    opacity: 0;
    visibility: hidden;
}

.toggle-panel {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255,255,255,0.95);
    border: none;
    width: 40px;
    height: 80px;
    border-radius: 0 20px 20px 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #667eea;
    box-shadow: 2px 0 10px rgba(0,0,0,0.2);
    transition: all 0.3s;
    z-index: 100;
}

.main-grid:not(.panel-hidden) .toggle-panel {
    left: 400px;
    border-radius: 20px 0 0 20px;
}

.toggle-panel:hover {
    background: white;
    width: 50px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 10px;
}

.section-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
}

.header-buttons {
    display: flex;
    gap: 10px;
}

.add-btn, .delete-mode-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 28px;
    border-radius: 30px;
    cursor: pointer;
    font-size: 1.1rem;
    transition: transform 0.2s;
}

.delete-mode-btn {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
}

.delete-mode-btn.active {
    background: linear-gradient(135deg, #c53030 0%, #9b2c2c 100%);
}

.add-btn:hover, .delete-mode-btn:hover {
    transform: scale(1.05);
}

.add-form {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 25px;
    display: none;
}

.add-form input {
    width: 100%;
    padding: 14px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1.2rem;
    margin-bottom: 15px;
}

.add-form input:focus {
    outline: none;
    border-color: #667eea;
}

.form-buttons {
    display: flex;
    gap: 12px;
}

.form-buttons button {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.2s;
}

.confirm-btn {
    background: #48bb78;
    color: white;
}

.cancel-btn {
    background: #e0e0e0;
    color: #333;
}

.delete-controls {
    display: none;
    background: #ffe5e5;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.delete-controls.active {
    display: block;
}

.delete-selected-btn {
    background: #e53e3e;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    width: 100%;
}

.employee-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 20px;
    overflow-y: auto;
    flex: 1;
    padding: 5px;
}

.return-zone {
    min-height: 100px;
    margin-bottom: 20px;
    padding: 20px;
    background: #e6f4ff;
    border: 3px dashed #667eea;
    border-radius: 15px;
    text-align: center;
    color: #667eea;
    font-weight: 600;
    display: none;
}

.return-zone.drag-over {
    background: #d1e7ff;
    border-color: #4c68d7;
}

.show-return-zone .return-zone {
    display: block;
}

.employee-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 18px;
    text-align: center;
    cursor: move;
    transition: all 0.3s;
    position: relative;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    height: fit-content;
}

.employee-card.delete-mode {
    cursor: pointer;
}

.employee-card.selected {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    transform: scale(0.95);
}

.employee-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

.employee-card.dragging {
    opacity: 0.5;
}

.employee-name {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.employee-icon {
    font-size: 2.5rem;
    margin-bottom: 12px;
}

.checkbox-wrapper {
    position: absolute;
    top: 8px;
    right: 8px;
    display: none;
}

.delete-mode .checkbox-wrapper {
    display: block;
}

.checkbox-wrapper input {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

.schedule-section {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    display: flex;
    flex-direction: column;
}

.status-bar {
    background: #f8f9fa;
    padding: 20px;
    border-bottom: 2px solid #e0e0e0;
    display: flex;
    justify-content: center;
    gap: 40px;
    font-size: 1.2rem;
    font-weight: 600;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
}

.status-dot.available {
    background: #48bb78;
}

.status-dot.busy {
    background: #e53e3e;
}

.status-dot.out {
    background: #f6ad55;
}

table {
    width: 100%;
    border-collapse: collapse;
    flex: 1;
}

td {
    padding: 30px;
    border-bottom: 2px solid #f0f0f0;
    vertical-align: middle;
}

tr:hover td {
    background: #fafafa;
}

.position-cell {
    font-weight: 600;
    font-size: 1.4rem;
    background: #f8f9fa;
    color: #2d3748;
    text-align: center;
    width: 200px;
}

.position-stylist {
    border-left: 12px solid #667eea;
    border-right: 6px solid #e2e8f0;
}

.position-assistant {
    border-left: 12px solid #48bb78;
    border-right: 6px solid #e2e8f0;
}

.project-cell {
    color: #4a5568;
    font-weight: 600;
    font-size: 1.3rem;
    border-right: 8px solid #d2d6dc;
    background: #f7fafc;
    text-align: center;
    width: 200px;
}

.employee-drop-zone {
    min-height: 140px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
    border: 3px dashed #e0e0e0;
    transition: all 0.3s;
    position: relative;
}

.employee-drop-zone.drag-over {
    background: #e6f4ff;
    border-color: #667eea;
}

.assigned-employees {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    position: relative;
}

.drag-placeholder {
    width: 150px;
    height: 120px;
    background: #e0e0e0;
    border: 2px dashed #999;
    border-radius: 15px;
    transition: all 0.3s ease;
    opacity: 0;
}

.drag-placeholder.show {
    opacity: 1;
}

.assigned-card {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    position: relative;
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.4);
    transition: all 0.3s ease;
    cursor: pointer;
    width: 150px;
    height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
}

.assigned-card.dragging {
    opacity: 0.5;
    transform: scale(0.95);
}

.assigned-card.shift-right {
    transform: translateX(10px);
}

.assigned-card.shift-left {
    transform: translateX(-10px);
}

.assigned-card.busy {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.4);
}

.assigned-card.out {
    background: linear-gradient(135deg, #f6ad55 0%, #ed8936 100%);
    box-shadow: 0 4px 12px rgba(237, 137, 54, 0.4);
}

.assigned-card:hover {
    transform: translateY(-3px);
}

.assigned-card .employee-icon {
    font-size: 2rem;
    margin-bottom: 8px;
}

.assigned-card .employee-name {
    font-size: 1.1rem;
    font-weight: 600;
}

.status-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: white;
}

/* Added to disable card transitions during active drag operations */
.assigned-employees.drag-active .assigned-card {
    transition: none !important;
}

@media (max-width: 1200px) {
    .employee-section {
        width: 350px;
    }

    .main-grid:not(.panel-hidden) .toggle-panel {
        left: 350px;
    }
    
    h1 {
        font-size: 2rem;
    }
}

@media (max-width: 968px) {
    .container {
        /* Flex properties from body and .container itself should handle adaptiveness */
    }
    
    .main-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .employee-section {
        width: 100% !important;
        min-height: 400px;
    }
    
    /* 
    .toggle-panel {
        display: none; 
    }
    */ /* This CSS block should remain commented out to ensure the toggle button is visible on small screens. */
} 