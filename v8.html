<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美发店排班管理系统</title>
    <link rel="stylesheet" href="style.css">
    <style>
        /* 自适应屏幕大小的样式覆盖 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html, body {
            height: 100%;
            overflow: hidden;
        }
        
        body {
            display: flex;
            flex-direction: column;
        }
        
        .container {
            flex: 1;
            padding: 10px;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }
        
        .header-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            flex-shrink: 0;
        }
        
        h1 {
            font-size: 2rem;
            margin: 0;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 15px;
            color: white;
        }
        
        .user-menu button {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .user-menu button:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .main-grid {
            flex: 1;
            gap: 20px;
            min-height: 0;
            display: grid;
            grid-template-columns: auto 1fr;
        }
        
        .main-grid.panel-hidden {
            grid-template-columns: 0 1fr;
        }
        
        .main-grid.not-logged-in {
            grid-template-columns: 1fr;
        }
        
        .employee-section {
            height: 100%;
            padding: 20px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .employee-cards {
            flex: 1;
            overflow-y: auto;
            min-height: 0;
        }
        
        .schedule-section {
            height: 100%;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .status-bar {
            flex-shrink: 0;
            padding: 15px 20px;
        }
        
        .table-wrapper {
            flex: 1;
            overflow: auto;
            min-height: 0;
        }
        
        table {
            width: 100%;
            height: 100%;
            table-layout: fixed;
        }
        
        td {
            padding: 20px;
            height: 25%;
            vertical-align: top;
        }
        
        .employee-drop-zone {
            min-height: 100%;
            height: 100%;
            padding: 15px;
            display: flex;
            align-items: flex-start;
        }
        
        .assigned-employees {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-content: flex-start;
        }
        
        .assigned-card {
            width: 120px;
            height: 100px;
            padding: 15px;
            flex-shrink: 0;
        }
        
        .assigned-card .employee-icon {
            font-size: 1.5rem;
            margin-bottom: 5px;
        }
        
        .assigned-card .employee-name {
            font-size: 0.9rem;
        }
        
        /* 登录模态框样式 */
        .login-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .login-modal.show {
            display: flex;
        }
        
        .login-content {
            background: white;
            padding: 40px;
            border-radius: 20px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .login-content h2 {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        
        .login-form input {
            width: 100%;
            padding: 12px;
            margin-bottom: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1rem;
        }
        
        .login-form input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .login-form button {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .login-form button:hover {
            transform: scale(1.02);
        }
        
        .error-message {
            color: #e53e3e;
            text-align: center;
            margin-bottom: 15px;
            display: none;
        }
        
        /* 修改密码模态框 */
        .password-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .password-modal.show {
            display: flex;
        }
        
        .password-content {
            background: white;
            padding: 40px;
            border-radius: 20px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .password-content h2 {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        
        .close-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }
        
        /* 修复拖放时的指针事件问题 */
        .drag-placeholder {
            pointer-events: none;
            width: 120px;
            height: 100px;
        }
        
        /* 只读模式样式 */
        .readonly-mode .add-btn,
        .readonly-mode .delete-mode-btn,
        .readonly-mode .employee-card,
        .readonly-mode .one-click-btn {
            display: none;
        }

        .readonly-mode .assigned-card {
            cursor: default;
        }

        .readonly-mode .assigned-card:hover {
            transform: none;
        }

        /* 项目单元格和一键按钮样式 */
        .project-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            height: 100%;
            justify-content: center;
        }

        .project-name {
            font-size: 1rem;
            font-weight: bold;
            color: #333;
        }

        .one-click-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 6px 12px;
            font-size: 11px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(255, 107, 107, 0.3);
            min-width: 40px;
            white-space: nowrap;
        }

        .one-click-btn:hover {
            background: linear-gradient(135deg, #ff5252 0%, #d63031 100%);
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(255, 107, 107, 0.4);
        }

        .one-click-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
        }

        .one-click-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        @media (max-width: 1200px) {
            h1 {
                font-size: 1.5rem;
            }
            
            .assigned-card {
                width: 100px;
                height: 80px;
                padding: 10px;
            }
            
            .assigned-card .employee-icon {
                font-size: 1.2rem;
            }
            
            .assigned-card .employee-name {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header-wrapper">
            <h1>✂️ 美发店排班管理系统</h1>
            <div class="user-menu" id="userMenu">
                <button id="loginBtn" onclick="showLoginModal()" style="display: none;">登录</button>
                <div id="loggedInMenu" style="display: none;">
                    <span id="usernameDisplay">admin</span>
                    <button onclick="showPasswordModal()">修改密码</button>
                    <button onclick="logout()">退出登录</button>
                </div>
            </div>
        </div>
        
        <div class="main-grid not-logged-in" id="mainGrid">
            <button class="toggle-panel" onclick="togglePanel()" aria-label="切换员工面板" style="display: none;">
                <span id="toggleIcon">▶</span>
            </button>
            
            <!-- 员工列表 -->
            <div class="employee-section" id="employeeSection" style="display: none;">
                <div class="section-header">
                    <h2 class="section-title">👥 员工列表</h2>
                    <div class="header-buttons">
                        <button class="add-btn" onclick="toggleAddForm()" role="button" aria-label="添加新员工">+ 添加</button>
                        <button class="delete-mode-btn" id="deleteModeBtn" onclick="toggleDeleteMode()" role="button" aria-label="切换删除模式">🗑 删除</button>
                    </div>
                </div>
                
                <div class="add-form" id="addForm">
                    <input type="text" id="newEmployeeName" placeholder="输入员工姓名" onkeypress="if(event.key==='Enter') addEmployee()">
                    <div class="form-buttons">
                        <button class="confirm-btn" onclick="addEmployee()" role="button" aria-label="确认添加">确定</button>
                        <button class="cancel-btn" onclick="toggleAddForm()" role="button" aria-label="取消添加">取消</button>
                    </div>
                </div>
                
                <div class="delete-controls" id="deleteControls">
                    <button class="delete-selected-btn" onclick="deleteSelected()" role="button" aria-label="删除选中的员工">删除选中的员工</button>
                </div>
                
                <div class="return-zone" ondrop="dropToReturn(event)" ondragover="allowDrop(event)" ondragleave="dragLeave(event)">
                    将员工拖到这里返回待分配列表
                </div>
                
                <div class="employee-cards" id="employeeList">
                    <!-- 员工卡片将在这里动态生成 -->
                </div>
            </div>
            
            <!-- 排班表格 -->
            <div class="schedule-section">
                <!-- 状态统计栏 -->
                <div class="status-bar">
                    <div class="status-item">
                        <div class="status-dot available"></div>
                        <span>闲：<span id="availableCount">0</span>人</span>
                    </div>
                    <div class="status-item">
                        <div class="status-dot busy"></div>
                        <span>忙：<span id="busyCount">0</span>人</span>
                    </div>
                    <div class="status-item">
                        <div class="status-dot out"></div>
                        <span>外出：<span id="outCount">0</span>人</span>
                    </div>
                </div>
                
                <div class="table-wrapper">
                    <table>
                        <tbody>
                            <tr>
                                <td rowspan="2" class="position-cell position-stylist">发型师</td>
                                <td class="project-cell">
                                    <div class="project-header">
                                        <span class="project-name">剪发</span>
                                        <button class="one-click-btn" onclick="moveFirstToLast('发型师-剪发')" title="将第一个员工移到最后">一键</button>
                                    </div>
                                </td>
                                <td>
                                    <div class="employee-drop-zone" data-position="发型师-剪发" ondrop="drop(event)" ondragover="allowDrop(event)" ondragleave="dragLeave(event)">
                                        <div class="assigned-employees" ondragover="handleDragOver(event)" ondrop="drop(event)"></div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="project-cell">
                                    <div class="project-header">
                                        <span class="project-name">烫染</span>
                                        <button class="one-click-btn" onclick="moveFirstToLast('发型师-烫染')" title="将第一个员工移到最后">一键</button>
                                    </div>
                                </td>
                                <td>
                                    <div class="employee-drop-zone" data-position="发型师-烫染" ondrop="drop(event)" ondragover="allowDrop(event)" ondragleave="dragLeave(event)">
                                        <div class="assigned-employees" ondragover="handleDragOver(event)" ondrop="drop(event)"></div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td rowspan="2" class="position-cell position-assistant">助理</td>
                                <td class="project-cell">
                                    <div class="project-header">
                                        <span class="project-name">洗发</span>
                                        <button class="one-click-btn" onclick="moveFirstToLast('助理-洗发')" title="将第一个员工移到最后">一键</button>
                                    </div>
                                </td>
                                <td>
                                    <div class="employee-drop-zone" data-position="助理-洗发" ondrop="drop(event)" ondragover="allowDrop(event)" ondragleave="dragLeave(event)">
                                        <div class="assigned-employees" ondragover="handleDragOver(event)" ondrop="drop(event)"></div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="project-cell">
                                    <div class="project-header">
                                        <span class="project-name">烫染</span>
                                        <button class="one-click-btn" onclick="moveFirstToLast('助理-烫染')" title="将第一个员工移到最后">一键</button>
                                    </div>
                                </td>
                                <td>
                                    <div class="employee-drop-zone" data-position="助理-烫染" ondrop="drop(event)" ondragover="allowDrop(event)" ondragleave="dragLeave(event)">
                                        <div class="assigned-employees" ondragover="handleDragOver(event)" ondrop="drop(event)"></div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录模态框 -->
    <div class="login-modal" id="loginModal">
        <div class="login-content">
            <button class="close-btn" onclick="hideLoginModal()" style="position: absolute; top: 15px; right: 15px;">×</button>
            <h2>登录系统</h2>
            <div class="error-message" id="loginError"></div>
            <form class="login-form" onsubmit="handleLogin(event)">
                <input type="text" id="username" placeholder="用户名" required>
                <input type="password" id="password" placeholder="密码" required>
                <button type="submit">登录</button>
            </form>
        </div>
    </div>

    <!-- 修改密码模态框 -->
    <div class="password-modal" id="passwordModal">
        <div class="password-content" style="position: relative;">
            <button class="close-btn" onclick="hidePasswordModal()">×</button>
            <h2>修改密码</h2>
            <div class="error-message" id="passwordError"></div>
            <form class="login-form" onsubmit="handlePasswordChange(event)">
                <input type="password" id="oldPassword" placeholder="原密码" required>
                <input type="password" id="newPassword" placeholder="新密码" required>
                <input type="password" id="confirmPassword" placeholder="确认新密码" required>
                <button type="submit">确认修改</button>
            </form>
        </div>
    </div>

    <script>
        // 员工数据
        let employees = [
            { id: 1, name: '小明' },
            { id: 2, name: '小红' },
            { id: 3, name: '小李' },
            { id: 4, name: '小张' },
            { id: 5, name: '小王' },
            { id: 6, name: '小刘' }
        ];
        
        // 未分配的员工
        let unassignedEmployees = [...employees];
        
        // 排班数据，包含状态
        let schedule = {
            '发型师-剪发': [],
            '发型师-烫染': [],
            '助理-洗发': [],
            '助理-烫染': []
        };
        
        // 员工状态（available=绿色空闲, busy=红色忙碌, out=黄色外出）
        let employeeStatus = {};
        
        let draggedEmployee = null;
        let draggedFromPosition = null;
        let draggedFromIndex = null;
        let deleteMode = false;
        let selectedForDelete = new Set();
        let currentPlaceholder = null;
        let isLoggedIn = false;

        const LOCAL_STORAGE_KEY = 'hairSalonScheduleApp';
        const AUTH_KEY = 'hairSalonAuth';
        const USER_KEY = 'hairSalonUser';

        // 检查登录状态
        function checkLoginStatus() {
            const auth = localStorage.getItem(AUTH_KEY);
            const user = localStorage.getItem(USER_KEY);
            if (auth === 'true' && user) {
                isLoggedIn = true;
                const userInfo = JSON.parse(user);
                document.getElementById('usernameDisplay').textContent = userInfo.username;
                showLoggedInUI();
            } else {
                isLoggedIn = false;
                showNotLoggedInUI();
            }
        }

        // 显示未登录界面
        function showNotLoggedInUI() {
            document.getElementById('loginModal').classList.remove('show');
            document.getElementById('mainGrid').classList.add('not-logged-in');
            document.getElementById('employeeSection').style.display = 'none';
            document.querySelector('.toggle-panel').style.display = 'none';
            document.getElementById('userMenu').style.display = 'flex';
            document.getElementById('loginBtn').style.display = 'block';
            document.getElementById('loggedInMenu').style.display = 'none';
            document.body.classList.add('readonly-mode');
        }

        // 显示登录模态框
        function showLoginModal() {
            document.getElementById('loginModal').classList.add('show');
        }

        // 隐藏登录模态框
        function hideLoginModal() {
            document.getElementById('loginModal').classList.remove('show');
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
            document.getElementById('loginError').style.display = 'none';
        }

        // 显示登录后的界面
        function showLoggedInUI() {
            document.getElementById('loginModal').classList.remove('show');
            document.getElementById('mainGrid').classList.remove('not-logged-in');
            document.getElementById('employeeSection').style.display = 'flex';
            document.querySelector('.toggle-panel').style.display = 'flex';
            document.getElementById('userMenu').style.display = 'flex';
            document.getElementById('loginBtn').style.display = 'none';
            document.getElementById('loggedInMenu').style.display = 'flex';
            document.body.classList.remove('readonly-mode');
        }

        // 处理登录
        function handleLogin(event) {
            event.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            // 获取存储的用户信息，如果没有则使用默认值
            let userInfo = localStorage.getItem(USER_KEY);
            if (!userInfo) {
                userInfo = { username: 'admin', password: 'admin' };
            } else {
                userInfo = JSON.parse(userInfo);
            }
            
            if (username === userInfo.username && password === userInfo.password) {
                isLoggedIn = true;
                localStorage.setItem(AUTH_KEY, 'true');
                localStorage.setItem(USER_KEY, JSON.stringify(userInfo));
                document.getElementById('usernameDisplay').textContent = username;
                showLoggedInUI();
                hideLoginModal();
            } else {
                document.getElementById('loginError').textContent = '用户名或密码错误';
                document.getElementById('loginError').style.display = 'block';
            }
        }

        // 退出登录
        function logout() {
            isLoggedIn = false;
            localStorage.removeItem(AUTH_KEY);
            showNotLoggedInUI();
        }

        // 显示修改密码模态框
        function showPasswordModal() {
            document.getElementById('passwordModal').classList.add('show');
        }

        // 隐藏修改密码模态框
        function hidePasswordModal() {
            document.getElementById('passwordModal').classList.remove('show');
            document.getElementById('oldPassword').value = '';
            document.getElementById('newPassword').value = '';
            document.getElementById('confirmPassword').value = '';
            document.getElementById('passwordError').style.display = 'none';
        }

        // 处理密码修改
        function handlePasswordChange(event) {
            event.preventDefault();
            const oldPassword = document.getElementById('oldPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            const userInfo = JSON.parse(localStorage.getItem(USER_KEY) || '{"username":"admin","password":"admin"}');
            
            if (oldPassword !== userInfo.password) {
                document.getElementById('passwordError').textContent = '原密码错误';
                document.getElementById('passwordError').style.display = 'block';
                return;
            }
            
            if (newPassword !== confirmPassword) {
                document.getElementById('passwordError').textContent = '两次输入的新密码不一致';
                document.getElementById('passwordError').style.display = 'block';
                return;
            }
            
            if (newPassword.length < 4) {
                document.getElementById('passwordError').textContent = '新密码长度至少4位';
                document.getElementById('passwordError').style.display = 'block';
                return;
            }
            
            // 更新密码
            userInfo.password = newPassword;
            localStorage.setItem(USER_KEY, JSON.stringify(userInfo));
            
            alert('密码修改成功！');
            hidePasswordModal();
        }

        // Helper function to manage drag-active class on drop zones
        function setDropZonesDragActive(isActive) {
            document.querySelectorAll('.assigned-employees').forEach(zone => {
                if (isActive) {
                    zone.classList.add('drag-active');
                } else {
                    zone.classList.remove('drag-active');
                }
            });
        }

        // 保存状态到 localStorage
        function saveStateToLocalStorage() {
            const state = {
                employees,
                unassignedEmployees,
                schedule,
                employeeStatus
            };
            localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(state));
        }

        // 从 localStorage 加载状态
        function loadStateFromLocalStorage() {
            const savedState = localStorage.getItem(LOCAL_STORAGE_KEY);
            if (savedState) {
                const state = JSON.parse(savedState);
                employees = state.employees || [];
                unassignedEmployees = state.unassignedEmployees || [...employees];
                schedule = state.schedule || {
                    '发型师-剪发': [],
                    '发型师-烫染': [],
                    '助理-洗发': [],
                    '助理-烫染': []
                };
                employeeStatus = state.employeeStatus || {};
                
                if (state.employees && !state.unassignedEmployees) {
                     const assignedIds = new Set();
                     Object.values(schedule).flat().forEach(emp => assignedIds.add(emp.id));
                     unassignedEmployees = employees.filter(emp => !assignedIds.has(emp.id));
                }

            } else {
                employees = [
                    { id: 1, name: '小明' },
                    { id: 2, name: '小红' },
                    { id: 3, name: '小李' },
                    { id: 4, name: '小张' },
                    { id: 5, name: '小王' },
                    { id: 6, name: '小刘' }
                ];
                unassignedEmployees = [...employees];
                schedule = {
                    '发型师-剪发': [],
                    '发型师-烫染': [],
                    '助理-洗发': [],
                    '助理-烫染': []
                };
                employeeStatus = {};
            }
        }

        // 初始化
        function init() {
            checkLoginStatus(); // 先检查登录状态
            loadStateFromLocalStorage();

            const mainGrid = document.getElementById('mainGrid');
            const icon = document.getElementById('toggleIcon');

            if (window.innerWidth <= 968) {
                mainGrid.classList.remove('panel-hidden');
                icon.textContent = '◀'; 
            } else {
                if (!localStorage.getItem(LOCAL_STORAGE_KEY) && !mainGrid.classList.contains('panel-hidden')) {
                    mainGrid.classList.add('panel-hidden');
                }
                if (mainGrid.classList.contains('panel-hidden')) {
                    icon.textContent = '▶';
                } else {
                    icon.textContent = '◀';
                }
            }
            
            renderEmployees();
            renderSchedule();
            updateStatusCount();
        }

        // 更新状态统计
        function updateStatusCount() {
            let availableCount = 0;
            let busyCount = 0;
            let outCount = 0;
            
            Object.keys(schedule).forEach(position => {
                schedule[position].forEach(emp => {
                    const status = employeeStatus[`${position}-${emp.id}`] || 'available';
                    if (status === 'available') availableCount++;
                    else if (status === 'busy') busyCount++;
                    else if (status === 'out') outCount++;
                });
            });
            
            document.getElementById('availableCount').textContent = availableCount;
            document.getElementById('busyCount').textContent = busyCount;
            document.getElementById('outCount').textContent = outCount;
        }

        // 切换面板显示/隐藏
        function togglePanel() {
            const mainGrid = document.getElementById('mainGrid');
            const icon = document.getElementById('toggleIcon');
            
            if (mainGrid.classList.contains('panel-hidden')) {
                mainGrid.classList.remove('panel-hidden');
                icon.textContent = '◀';
            } else {
                mainGrid.classList.add('panel-hidden');
                icon.textContent = '▶';
            }
        }

        // 渲染员工列表
        function renderEmployees() {
            const employeeList = document.getElementById('employeeList');
            employeeList.innerHTML = unassignedEmployees.map(emp => `
                <div class="employee-card ${deleteMode ? 'delete-mode' : ''} ${selectedForDelete.has(emp.id) ? 'selected' : ''}" 
                     draggable="${!deleteMode}" 
                     ondragstart="drag(event)" 
                     ondragend="cleanupDrag()"
                     onclick="handleEmployeeCardClick(${emp.id})"
                     onkeydown="handleEmployeeCardKeydown(event, ${emp.id})"
                     data-employee='${JSON.stringify(emp)}'
                     role="button" 
                     tabindex="0" 
                     aria-label="${emp.name}${deleteMode ? (selectedForDelete.has(emp.id) ? ', 已选中待删除' : ', 点击以选中待删除') : ',拖动以分配'}">
                    <div class="checkbox-wrapper">
                        <input type="checkbox" ${selectedForDelete.has(emp.id) ? 'checked' : ''} 
                               onclick="event.stopPropagation(); selectForDelete(${emp.id})"
                               aria-hidden="true"> 
                    </div>
                    <div class="employee-icon">👤</div>
                    <div class="employee-name">${emp.name}</div>
                </div>
            `).join('');
        }

        // 渲染排班表
        function renderSchedule() {
            Object.keys(schedule).forEach(position => {
                const dropZone = document.querySelector(`[data-position="${position}"] .assigned-employees`);
                if (!dropZone) {
                    console.error(`Drop zone not found for position: ${position}`);
                    return;
                }
                dropZone.innerHTML = schedule[position].map((emp, index) => {
                    const status = employeeStatus[`${position}-${emp.id}`] || 'available';
                    let statusClass = '';
                    let statusText = '空闲';
                    if (status === 'busy') {
                        statusClass = 'busy';
                        statusText = '忙碌';
                    } else if (status === 'out') {
                        statusClass = 'out';
                        statusText = '外出';
                    }
                    
                    const draggableAttr = isLoggedIn ? 'draggable="true"' : '';
                    const dragHandlers = isLoggedIn ? `ondragstart="dragFromSchedule(event, '${position}', ${index})" ondragend="cleanupDrag()"` : '';
                    const clickHandler = isLoggedIn ? `onclick="toggleStatus('${position}', ${emp.id})"` : '';
                    
                    return `
                        <div class="assigned-card ${statusClass}" 
                             ${draggableAttr}
                             ${dragHandlers}
                             ${clickHandler}
                             onkeydown="handleAssignedCardKeydown(event, '${position}', ${emp.id})"
                             data-employee='${JSON.stringify(emp)}'
                             data-index="${index}"
                             role="button"
                             tabindex="0"
                             aria-label="${emp.name}, 当前状态: ${statusText}${isLoggedIn ? ', 点击切换状态或拖动以重新分配' : ''}">
                            <div class="status-indicator"></div>
                            <div class="employee-icon">👤</div>
                            <div class="employee-name">${emp.name}</div>
                        </div>
                    `;
                }).join('');
            });
            updateStatusCount();
            saveStateToLocalStorage();
        }

        // 切换状态（循环：空闲->忙碌->外出->空闲）
        function toggleStatus(position, employeeId) {
            if (!isLoggedIn) return;

            console.log(`toggleStatus called for position: ${position}, employeeId: ${employeeId}`);
            const key = `${position}-${employeeId}`;
            const currentStatus = employeeStatus[key] || 'available';
            console.log(`Current status for ${key}: ${currentStatus}`);

            if (currentStatus === 'available') {
                employeeStatus[key] = 'busy';
            } else if (currentStatus === 'busy') {
                employeeStatus[key] = 'out';
            } else {
                employeeStatus[key] = 'available';
            }
            console.log(`New status for ${key}: ${employeeStatus[key]}`);

            renderSchedule();
            saveStateToLocalStorage();
            console.log('renderSchedule and saveStateToLocalStorage called after toggleStatus');
        }

        // 一键功能：将第一个员工移到最后
        function moveFirstToLast(position) {
            if (!isLoggedIn) return;

            console.log(`moveFirstToLast called for position: ${position}`);

            // 检查该位置是否有员工
            if (!schedule[position] || schedule[position].length === 0) {
                console.log(`No employees in position: ${position}`);
                return;
            }

            // 如果只有一个员工，不需要移动
            if (schedule[position].length === 1) {
                console.log(`Only one employee in position: ${position}, no need to move`);
                return;
            }

            // 将第一个员工移到最后
            const firstEmployee = schedule[position].shift(); // 移除第一个
            schedule[position].push(firstEmployee); // 添加到最后

            console.log(`Moved ${firstEmployee.name} from first to last in ${position}`);
            console.log(`New order in ${position}:`, schedule[position].map(emp => emp.name));

            // 重新渲染并保存
            renderSchedule();
            saveStateToLocalStorage();
        }

        // 切换删除模式
        function toggleDeleteMode() {
            if (!isLoggedIn) return;
            
            deleteMode = !deleteMode;
            selectedForDelete.clear();
            
            const btn = document.getElementById('deleteModeBtn');
            btn.setAttribute('aria-pressed', deleteMode.toString());
            const controls = document.getElementById('deleteControls');
            
            if (deleteMode) {
                btn.classList.add('active');
                controls.classList.add('active');
            } else {
                btn.classList.remove('active');
                controls.classList.remove('active');
            }
            
            renderEmployees();
        }

        // 选择要删除的员工
        function selectForDelete(id) {
            if (!deleteMode || !isLoggedIn) return;
            
            if (selectedForDelete.has(id)) {
                selectedForDelete.delete(id);
            } else {
                selectedForDelete.add(id);
            }
            
            renderEmployees();
        }

        // 删除选中的员工
        function deleteSelected() {
            if (!isLoggedIn || selectedForDelete.size === 0) return;
            
            const idsToDelete = new Set(selectedForDelete);
            
            // 从未分配列表中删除
            unassignedEmployees = unassignedEmployees.filter(emp => !idsToDelete.has(emp.id));
            
            // 从总员工列表中删除
            employees = employees.filter(emp => !idsToDelete.has(emp.id));
            
            // 从所有排班中删除
            Object.keys(schedule).forEach(position => {
                schedule[position] = schedule[position].filter(emp => !idsToDelete.has(emp.id));
            });
            
            // 从 employeeStatus 中删除相关员工的状态信息
            idsToDelete.forEach(id => {
                Object.keys(employeeStatus).forEach(key => {
                    if (key.endsWith(`-${id}`)) {
                        delete employeeStatus[key];
                    }
                });
            });

            selectedForDelete.clear();
            deleteMode = false;
            document.getElementById('deleteModeBtn').classList.remove('active');
            document.getElementById('deleteModeBtn').setAttribute('aria-pressed', 'false');
            document.getElementById('deleteControls').classList.remove('active');
            
            renderEmployees();
            renderSchedule();
            saveStateToLocalStorage();
        }

        // 切换添加表单
        function toggleAddForm() {
            if (!isLoggedIn) return;
            
            const form = document.getElementById('addForm');
            const isHidden = getComputedStyle(form).display === 'none';
            form.style.display = isHidden ? 'block' : 'none';
            if (isHidden) {
                document.getElementById('newEmployeeName').focus();
            }
        }

        // 添加员工
        function addEmployee() {
            if (!isLoggedIn) return;
            
            const input = document.getElementById('newEmployeeName');
            const name = input.value.trim();
            
            if (name) {
                const newEmployee = {
                    id: Date.now() + Math.random(),
                    name: name
                };
                employees.push(newEmployee);
                unassignedEmployees.push(newEmployee);
                renderEmployees();
                input.value = '';
                toggleAddForm();
                saveStateToLocalStorage();
            }
        }

        // 拖拽功能
        function drag(event) {
            if (deleteMode || !isLoggedIn) return;
            
            const card = event.target.closest('.employee-card');
            if (!card) return;
            setDropZonesDragActive(true);
            card.classList.add('dragging');
            draggedEmployee = JSON.parse(card.dataset.employee);
            event.dataTransfer.setData('text/plain', card.dataset.employee); 
            draggedFromPosition = null;
            draggedFromIndex = null;
            event.dataTransfer.effectAllowed = 'move';
        }

        // 从排班表拖拽
        function dragFromSchedule(event, position, index) {
            if (!isLoggedIn) return;
            
            const card = event.target.closest('.assigned-card');
            if (!card) return;
            setDropZonesDragActive(true);
            card.classList.add('dragging');
            draggedEmployee = JSON.parse(card.dataset.employee);
            event.dataTransfer.setData('text/plain', card.dataset.employee); 
            draggedFromPosition = position;
            draggedFromIndex = index;
            event.dataTransfer.effectAllowed = 'move';
            
            document.getElementById('employeeSection').classList.add('show-return-zone');
        }

        // 处理拖拽悬停 - 支持多行布局的拖拽逻辑
        function handleDragOver(event) {
            if (!draggedEmployee || !isLoggedIn) return;
            event.preventDefault();

            const container = event.currentTarget;
            const actualCards = [...container.children].filter(el =>
                el.classList.contains('assigned-card') &&
                !el.classList.contains('dragging') &&
                !el.classList.contains('drag-placeholder')
            );

            // 创建或获取占位符
            if (!currentPlaceholder) {
                currentPlaceholder = document.createElement('div');
                currentPlaceholder.className = 'drag-placeholder show';
                currentPlaceholder.style.width = '120px';
                currentPlaceholder.style.height = '100px';
                currentPlaceholder.style.border = '2px dashed #667eea';
                currentPlaceholder.style.borderRadius = '10px';
                currentPlaceholder.style.backgroundColor = 'rgba(102, 126, 234, 0.1)';
                currentPlaceholder.style.display = 'flex';
                currentPlaceholder.style.alignItems = 'center';
                currentPlaceholder.style.justifyContent = 'center';
                currentPlaceholder.style.color = '#667eea';
                currentPlaceholder.style.fontSize = '12px';
                currentPlaceholder.textContent = '放置位置';
                currentPlaceholder.style.flexShrink = '0';
            }

            const mouseX = event.clientX;
            const mouseY = event.clientY;
            let insertBeforeElement = null;

            if (actualCards.length === 0) {
                // 容器为空，直接插入
                insertBeforeElement = null;
            } else {
                // 创建插入点数组，考虑二维布局
                const insertPoints = [];

                // 为每张卡片创建插入点
                for (let i = 0; i < actualCards.length; i++) {
                    const card = actualCards[i];
                    const rect = card.getBoundingClientRect();

                    // 在卡片左侧添加插入点（插入到该卡片之前）
                    insertPoints.push({
                        x: rect.left - 5,
                        y: rect.top + rect.height / 2,
                        element: card,
                        position: 'before',
                        index: i
                    });
                }

                // 单独添加"插入到最后"的插入点
                if (actualCards.length > 0) {
                    const lastCard = actualCards[actualCards.length - 1];
                    const lastRect = lastCard.getBoundingClientRect();
                    insertPoints.push({
                        x: lastRect.right + 5,
                        y: lastRect.top + lastRect.height / 2,
                        element: null,  // null表示插入到最后
                        position: 'end',
                        index: actualCards.length
                    });
                }

                // 找到最接近鼠标位置的插入点（考虑X和Y坐标）
                let closestPoint = insertPoints[0];
                let minDistance = Math.sqrt(
                    Math.pow(mouseX - insertPoints[0].x, 2) +
                    Math.pow(mouseY - insertPoints[0].y, 2)
                );

                for (let i = 1; i < insertPoints.length; i++) {
                    const point = insertPoints[i];
                    const distance = Math.sqrt(
                        Math.pow(mouseX - point.x, 2) +
                        Math.pow(mouseY - point.y, 2)
                    );

                    if (distance < minDistance) {
                        minDistance = distance;
                        closestPoint = point;
                    }
                }

                insertBeforeElement = closestPoint.element;

                // 特殊处理：如果鼠标在容器的右下角区域，强制插入到最后
                const containerRect = container.getBoundingClientRect();
                const lastCard = actualCards[actualCards.length - 1];
                const lastRect = lastCard.getBoundingClientRect();

                // 如果鼠标在最后一张卡片的右侧或下方，且距离容器边界较近，插入到最后
                if ((mouseX > lastRect.right || mouseY > lastRect.bottom) &&
                    mouseX < containerRect.right - 20 &&
                    mouseY < containerRect.bottom - 20) {
                    insertBeforeElement = null;
                }
            }

            // 检查是否需要移动占位符
            let needsMove = true;
            if (currentPlaceholder.parentNode === container) {
                if (insertBeforeElement) {
                    needsMove = currentPlaceholder.nextSibling !== insertBeforeElement;
                } else {
                    needsMove = currentPlaceholder !== container.lastChild;
                }
            }

            // 移动占位符到正确位置
            if (needsMove) {
                if (insertBeforeElement) {
                    container.insertBefore(currentPlaceholder, insertBeforeElement);
                } else {
                    container.appendChild(currentPlaceholder);
                }
            }
        }

        function allowDrop(event) {
            if (!isLoggedIn) return;
            event.preventDefault();
            event.currentTarget.classList.add('drag-over');
        }

        function dragLeave(event) {
            event.currentTarget.classList.remove('drag-over');

            // 只有当鼠标真正离开容器时才清理占位符
            // 检查鼠标是否还在容器内
            const rect = event.currentTarget.getBoundingClientRect();
            const mouseX = event.clientX;
            const mouseY = event.clientY;

            const isOutside = mouseX < rect.left || mouseX > rect.right ||
                             mouseY < rect.top || mouseY > rect.bottom;

            if (isOutside && currentPlaceholder && currentPlaceholder.parentNode === event.currentTarget) {
                currentPlaceholder.remove();
                currentPlaceholder = null;
            }
        }

        function drop(event) {
            if (!isLoggedIn) return;
            
            event.preventDefault();
            event.stopPropagation();
            
            let dropZoneElement = event.currentTarget;
            
            if (!dropZoneElement.classList.contains('employee-drop-zone')) {
                dropZoneElement = dropZoneElement.closest('.employee-drop-zone');
            }
            
            if (!dropZoneElement) {
                console.error('Could not find drop zone element');
                cleanupDrag();
                return;
            }
            
            dropZoneElement.classList.remove('drag-over');
            
            console.log('Drop event triggered', event);
            if (!draggedEmployee) {
                console.error('draggedEmployee is null in drop');
                cleanupDrag();
                return;
            }
            
            const targetPosition = dropZoneElement.dataset.position;
            const container = dropZoneElement.querySelector('.assigned-employees');
            console.log(`Target position: ${targetPosition}, Dragged employee:`, draggedEmployee);

            if (!container) {
                console.error('Drop container (.assigned-employees) not found in drop zone:', dropZoneElement);
                cleanupDrag();
                return;
            }
            
            // 计算插入位置
            let insertIndex = 0;
            let isInsertAtEnd = false;

            if (currentPlaceholder && currentPlaceholder.parentNode === container) {
                // 获取占位符在容器中的位置
                const allChildren = Array.from(container.children);
                const placeholderIndex = allChildren.indexOf(currentPlaceholder);
                console.log(`Placeholder found at index: ${placeholderIndex}`);

                // 检查占位符是否在最后位置
                isInsertAtEnd = (placeholderIndex === allChildren.length - 1);

                // 移除占位符
                currentPlaceholder.remove();
                currentPlaceholder = null;

                // 重新计算实际的插入位置（因为移除了占位符）
                const actualCards = [...container.children].filter(el =>
                    el.classList.contains('assigned-card') &&
                    !el.classList.contains('dragging')
                );

                if (isInsertAtEnd) {
                    // 如果占位符在最后，插入到最后
                    insertIndex = actualCards.length;
                    console.log(`Insert at end, index: ${insertIndex}`);
                } else {
                    // 否则插入到占位符原来的位置
                    insertIndex = Math.min(placeholderIndex, actualCards.length);
                    console.log(`Insert at middle, index: ${insertIndex}`);
                }
            } else {
                // 没有占位符，插入到末尾
                insertIndex = container.children.length;
                isInsertAtEnd = true;
                console.warn('Placeholder not found, appending to end.');

                if (currentPlaceholder) {
                    currentPlaceholder.remove();
                    currentPlaceholder = null;
                }
            }

            if (draggedFromPosition) {
                console.log(`Dragged from position: ${draggedFromPosition}, index: ${draggedFromIndex}`);
                const originalArray = schedule[draggedFromPosition];
                if (originalArray && draggedFromIndex < originalArray.length) {
                    originalArray.splice(draggedFromIndex, 1);
                } else {
                    console.error('Error removing from original position:', draggedFromPosition, draggedFromIndex, originalArray);
                }

                // 只有在同一位置拖拽且不是插入到最后时，才调整索引
                if (draggedFromPosition === targetPosition && !isInsertAtEnd && draggedFromIndex < insertIndex) {
                    insertIndex--;
                    console.log(`Adjusted insert index for same position drag: ${insertIndex}`);
                }

                if (draggedFromPosition !== targetPosition) {
                    delete employeeStatus[`${draggedFromPosition}-${draggedEmployee.id}`];
                    console.log(`Cleared status for employee ${draggedEmployee.id} from old position ${draggedFromPosition}`);
                }
            } else {
                unassignedEmployees = unassignedEmployees.filter(emp => emp.id !== draggedEmployee.id);
                console.log(`Removed ${draggedEmployee.name} from unassignedEmployees`);
            }
            
            if (!schedule[targetPosition]) {
                console.error(`Target position ${targetPosition} does not exist in schedule object.`);
                schedule[targetPosition] = [];
            }
            schedule[targetPosition].splice(insertIndex, 0, draggedEmployee);
            console.log(`Inserted ${draggedEmployee.name} into ${targetPosition} at index ${insertIndex}`);
            
            if (!employeeStatus[`${targetPosition}-${draggedEmployee.id}`]) {
                employeeStatus[`${targetPosition}-${draggedEmployee.id}`] = 'available';
                console.log(`Set default status for ${draggedEmployee.name} in ${targetPosition} to available`);
            }
            
            renderEmployees();
            renderSchedule();
            saveStateToLocalStorage();
            cleanupDrag();
            console.log('Drop processing finished.');
        }

        // 拖回到待分配列表
        function dropToReturn(event) {
            if (!isLoggedIn) return;
            
            event.preventDefault();
            event.currentTarget.classList.remove('drag-over');
            
            if (!draggedEmployee || !draggedFromPosition) return;
            
            const returnedEmployee = schedule[draggedFromPosition].splice(draggedFromIndex, 1)[0];
            delete employeeStatus[`${draggedFromPosition}-${returnedEmployee.id}`];
            
            if (!unassignedEmployees.find(emp => emp.id === returnedEmployee.id)) {
                unassignedEmployees.push(returnedEmployee);
                unassignedEmployees.sort((a, b) => a.name.localeCompare(b.name));
            }
            
            renderEmployees();
            renderSchedule();
            cleanupDrag();
            saveStateToLocalStorage();
        }

        // 清理拖拽状态
        function cleanupDrag() {
            setDropZonesDragActive(false);

            document.querySelectorAll('.employee-card, .assigned-card').forEach(card => {
                card.classList.remove('dragging', 'shift-left', 'shift-right');
            });
            
            if (currentPlaceholder) {
                currentPlaceholder.remove();
                currentPlaceholder = null;
            }
            
            document.getElementById('employeeSection').classList.remove('show-return-zone');
            
            draggedEmployee = null;
            draggedFromPosition = null;
            draggedFromIndex = null;
        }

        // 为员工卡片添加键盘交互
        function handleEmployeeCardClick(id) {
            if (!isLoggedIn) return;
            if (deleteMode) {
                selectForDelete(id);
            }
        }

        function handleEmployeeCardKeydown(event, id) {
            if (!isLoggedIn) return;
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                handleEmployeeCardClick(id);
            }
        }

        // 为已分配的员工卡片添加键盘交互
        function handleAssignedCardKeydown(event, position, employeeId) {
            if (!isLoggedIn) return;
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                toggleStatus(position, employeeId);
            }
        }

        // 页面加载完成后初始化
        window.onload = init;
    </script>
</body>
</html>